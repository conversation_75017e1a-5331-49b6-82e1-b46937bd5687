export const moduleHomeIndex = "index";
export const hiddenModuleItemList = [moduleHomeIndex];
const HiddenRoutesPrefix = "/hidden";
export const HiddenRoutes = {
  SETTINGS: `${HiddenRoutesPrefix}/settings`,
  AUTH: `${HiddenRoutesPrefix}/auth`,
  PERMISSION: `${HiddenRoutesPrefix}/permission`,
  JOB_CATEGORY: `${HiddenRoutesPrefix}/job_category`,
  DoubleGuard: `${HiddenRoutesPrefix}/double_guard`,
  SpecialWork: `${HiddenRoutesPrefix}/special_work`,
  Sensor: `${HiddenRoutesPrefix}/sensor`,
  Equipment: `${HiddenRoutesPrefix}/equipment`,
};

const SystemSettingsRoutesPrefix = "/system";
export const SystemSettingsRoutes = {
  OM_CONFIG: `${SystemSettingsRoutesPrefix}/om_config`,
  BIGSCREEN_LEGEND: `${SystemSettingsRoutesPrefix}/bigscreen_legend`,
  DIC: `${SystemSettingsRoutesPrefix}/dic`,
  NOTICE_PUSH_CONFIG: `${SystemSettingsRoutesPrefix}/notice_push_config`,
  ALARM_PUSH_CONFIG: `${SystemSettingsRoutesPrefix}/alarm_push_config`,
  LOGINRECORDS: `${SystemSettingsRoutesPrefix}/loginRecords`,
  ACTIONRECORDS: `${SystemSettingsRoutesPrefix}/actionRecords`,
  CALENDAR: `${SystemSettingsRoutesPrefix}/calendar`,
  ABOUT: `${SystemSettingsRoutesPrefix}/about`,
};

const BasicRoutesPrefix = "/bim";
const BasicInfoRoutesPrefix = `${BasicRoutesPrefix}/info`;
const BasicCertificateRoutesPrefix = `${BasicRoutesPrefix}/certificate`;
const BasicProductiveProcessRoutesPrefix = `${BasicRoutesPrefix}/productive_process`;
const BasicEquipmentRoutesPrefix = `${BasicRoutesPrefix}/equipment`;
const BasicHRRoutesPrefix = `${BasicRoutesPrefix}/hr`;
const BasicContractorRoutesPrefix = `${BasicRoutesPrefix}/contractor`;
const BasicDocumentRoutesPrefix = `${BasicRoutesPrefix}/document`;
export const BasicRoutes = {
  ENTERPRISE_INFO_DASHBOARD: `${BasicRoutesPrefix}/${moduleHomeIndex}`,
  ENTERPRISE_INFO: `${BasicInfoRoutesPrefix}/enterprise_info`,
  ENTERPRISE_AREA: `${BasicInfoRoutesPrefix}/enterprise_area`,
  RISK_MANAGEMENT: `${BasicInfoRoutesPrefix}/risk_management`,
  ANNOUNCEMENT: `${BasicInfoRoutesPrefix}/announcement`,

  CERTIFICATE: `${BasicCertificateRoutesPrefix}/certificate`,
  LAW_REGULATION: `${BasicCertificateRoutesPrefix}/law_regulation`,

  MAJOR_HAZARD: `${BasicProductiveProcessRoutesPrefix}/major_hazard`,
  TOXIC_FLAMMABLE_GAS: `${BasicProductiveProcessRoutesPrefix}/toxic_flammable_gas`,
  DANGEROUS_PROCESS: `${BasicProductiveProcessRoutesPrefix}/dangerous_process`,
  PRODUCTION_UNIT: `${BasicProductiveProcessRoutesPrefix}/production_unit`,
  STORAGE_TANK_AREA: `${BasicProductiveProcessRoutesPrefix}/storage_tank_area`,
  STORAGE_TANK: `${BasicProductiveProcessRoutesPrefix}/storage_tank`,
  WAREHOUSE_AREA: `${BasicProductiveProcessRoutesPrefix}/warehouse_area`,
  WAREHOUSE: `${BasicProductiveProcessRoutesPrefix}/warehouse`,
  CHEMICAL: `${BasicProductiveProcessRoutesPrefix}/chemical`,
  STORAGE_RECORD: `${BasicProductiveProcessRoutesPrefix}/storage_record`,
  INTERLOCK: `${BasicProductiveProcessRoutesPrefix}/interlock`,

  EQUIPMENT_CATEGORY: `${BasicEquipmentRoutesPrefix}/equipment_category`,
  EQUIPMENT: `${BasicEquipmentRoutesPrefix}/equipment`,

  DEPARTMENT: `${BasicHRRoutesPrefix}/department`,
  POSITION: `${BasicHRRoutesPrefix}/position`,
  EMPLOYEE: `${BasicHRRoutesPrefix}/employee`,
  ROLE: `${BasicHRRoutesPrefix}/role`,
  GROUP: `${BasicHRRoutesPrefix}/group`,
  EMPLOYEE_CERTIFICATE: `${BasicHRRoutesPrefix}/employee_certificate`,

  CONTRACTOR: `${BasicContractorRoutesPrefix}/contractor`,
  CONTRACTOR_CERTIFICATE: `${BasicContractorRoutesPrefix}/contractor_certificate`,
  CONTRACTOR_EMPLOYEE: `${BasicContractorRoutesPrefix}/contractor_employee`,
  CONTRACTOR_EMPLOYEE_CERTIFICATE: `${BasicContractorRoutesPrefix}/contractor_employee_certificate`,
  CONTRACTOR_PROJECT: `${BasicContractorRoutesPrefix}/contractor_project`,
  CONTRACTOR_ACCIDENT: `${BasicContractorRoutesPrefix}/contractor_accident`,

  DOCUMENT_CATEGORY: `${BasicDocumentRoutesPrefix}/document_category`,
  DOCUMENT_INFORMATION: `${BasicDocumentRoutesPrefix}/document_information`,
};

const DoubleGuardRoutesPrefix = "/double_guard";
const DoubleGuardRiskMapRoutesPrefix = `${DoubleGuardRoutesPrefix}/risk_map`;
const DoubleGuardCardRoutesPrefix = `${DoubleGuardRoutesPrefix}/card`;
const DoubleGuardBbTaskRoutesPrefix = `${DoubleGuardRoutesPrefix}/bb`;
const DoubleGuardDangerRoutesPrefix = `${DoubleGuardRoutesPrefix}/danger`;
const DoubleGuardIncentiveRoutesPrefix = `${DoubleGuardRoutesPrefix}/incentive`;
const DoubleGuardSnapRoutesPrefix = `${DoubleGuardRoutesPrefix}/snap`;
const DoubleGuardGovSupervisionRoutesPrefix = `${DoubleGuardRoutesPrefix}/gov_supervision`;
const DoubleGuardEnterpriseSelfInspectionRoutesPrefix = `${DoubleGuardRoutesPrefix}/enterprise_self_inspection`;
export const DoubleGuardRoutes = {
  DASHBOARD: `${DoubleGuardRiskMapRoutesPrefix}/${moduleHomeIndex}`,
  // 风险分级管控
  RISK_AREA: `${DoubleGuardRiskMapRoutesPrefix}/risk_area`,
  RISK_OBJECT: `${DoubleGuardRiskMapRoutesPrefix}/risk_object`,
  RISK_UNIT: `${DoubleGuardRiskMapRoutesPrefix}/risk_unit`,
  RISK_EVENT: `${DoubleGuardRiskMapRoutesPrefix}/risk_event`,
  RISK_CONTROL_MEASURE: `${DoubleGuardRiskMapRoutesPrefix}/risk_control_measure`,
  // 两单三卡
  AWARENESS_CARD: `${DoubleGuardCardRoutesPrefix}/awareness_card`,
  EMERGENCY_CARD: `${DoubleGuardCardRoutesPrefix}/emergency_card`,
  SAFETY_CARD: `${DoubleGuardCardRoutesPrefix}/safety_card`,
  IDENTIFICATION_CHECKLIST: `${DoubleGuardCardRoutesPrefix}/identification_checklist`,
  CONTROL_CHECKLIST: `${DoubleGuardCardRoutesPrefix}/control_checklist`,
  // 包保责任
  BB_TEMPLATE: `${DoubleGuardBbTaskRoutesPrefix}/bb_template`,
  BB_CHECK: `${DoubleGuardBbTaskRoutesPrefix}/bb_check`,
  BB_TASK: `${DoubleGuardBbTaskRoutesPrefix}/bb_task`,
  BB_RECORD: `${DoubleGuardBbTaskRoutesPrefix}/bb_record`,
  BB_STAT: `${DoubleGuardBbTaskRoutesPrefix}/bb_stat`,
  /// 隐患排查治理
  IMEI: `${DoubleGuardDangerRoutesPrefix}/imei`,
  ALLOCATION: `${DoubleGuardDangerRoutesPrefix}/allocation`,
  TASK_LIST: `${DoubleGuardDangerRoutesPrefix}/task_list`,
  CHECK_RECORD: `${DoubleGuardDangerRoutesPrefix}/check_record`,
  DANGER: `${DoubleGuardDangerRoutesPrefix}/danger`,
  // 激励约束机制
  INCENTIVE: `${DoubleGuardIncentiveRoutesPrefix}/incentive`,
  EFFECT_OLD: `${DoubleGuardIncentiveRoutesPrefix}/effect_old`,
  EFFECT_NEW: `${DoubleGuardIncentiveRoutesPrefix}/effect_new`,
  // 随手拍
  SNAP: `${DoubleGuardSnapRoutesPrefix}/snap`,
  // 政府专项
  GOV_SUPERVISION_SETTINGS: `${DoubleGuardGovSupervisionRoutesPrefix}/settings`,
  GOV_SUPERVISION_CHECK_TASK: `${DoubleGuardGovSupervisionRoutesPrefix}/check_task`,
  GOV_SUPERVISION_CHECK_RECORD: `${DoubleGuardGovSupervisionRoutesPrefix}/check_record`,
  // 企业自查
  ENTERPRISE_SELF_INSPECTION_CATEGORY: `${DoubleGuardEnterpriseSelfInspectionRoutesPrefix}/category`,
  ENTERPRISE_SELF_INSPECTION_ITEM: `${DoubleGuardEnterpriseSelfInspectionRoutesPrefix}/item`,
  ENTERPRISE_SELF_INSPECTION_PLAN: `${DoubleGuardEnterpriseSelfInspectionRoutesPrefix}/plan`,
  ENTERPRISE_SELF_INSPECTION_TASK: `${DoubleGuardEnterpriseSelfInspectionRoutesPrefix}/task`,
};

const SpecialWorkRoutesPrefix = "/special_work";
const SpecialWorkConfigRoutesPrefix = `${SpecialWorkRoutesPrefix}/config`;
const SpecialWorkInfoRoutesPrefix = `${SpecialWorkRoutesPrefix}/info`;
const SpecialWorkProcessRoutesPrefix = `${SpecialWorkRoutesPrefix}/process`;
export const SpecialWorkRoutes = {
  DASHBOARD: `${SpecialWorkConfigRoutesPrefix}/${moduleHomeIndex}`,
  SW_CONFIG: `${SpecialWorkConfigRoutesPrefix}/sw_config`,
  JS_TEMPLATE_USER: `${SpecialWorkConfigRoutesPrefix}/js_template_user`,
  CODE_CONFIG: `${SpecialWorkConfigRoutesPrefix}/code_config`,
  PROCESS_TEMPLATE: `${SpecialWorkConfigRoutesPrefix}/process_template`,
  APPOINTMENT_PROCESS_TEMPLATE: `${SpecialWorkConfigRoutesPrefix}/appointment_process_template`,
  GAS_INTERVAL: `${SpecialWorkConfigRoutesPrefix}/gas_interval`,
  JOB_SLICE_INTERVAL: `${SpecialWorkConfigRoutesPrefix}/job_slice_interval`,
  FORM_CONFIG: `${SpecialWorkConfigRoutesPrefix}/form_config`,
  PRINT_CONFIG: `${SpecialWorkConfigRoutesPrefix}/print_config`,
  JS_TEMPLATE: `${SpecialWorkConfigRoutesPrefix}/js_template`,

  SAFETY_ANALYSIS: `${SpecialWorkInfoRoutesPrefix}/safety_analysis`,
  SAFETY_MEASURE: `${SpecialWorkInfoRoutesPrefix}/safety_measure`,
  RISK_MEASURE: `${SpecialWorkInfoRoutesPrefix}/risk_measure`,
  GAS_STANDARD: `${SpecialWorkInfoRoutesPrefix}/gas_standard`,
  SAFETY_DISCLOSURE: `${SpecialWorkInfoRoutesPrefix}/safety_disclosure`,

  JOB_APPOIMENT: `${SpecialWorkProcessRoutesPrefix}/job_appoiment`,
  JOB_TMPL_LIST: `${SpecialWorkProcessRoutesPrefix}/job_tmpl_list`,
  JOB_TMPL: `${SpecialWorkProcessRoutesPrefix}/job_tmpl`,

  PREVIEW: `${SpecialWorkRoutesPrefix}/preview`,
  // PRINT_PREVIEW: `${SpecialWorkRoutesPrefix}/print_preview`,
  PRINT_PREVIEW: `/print_preview`,
};

const MajorHazardRoutesPrefix = "/major_hazard";
const MajorHazardAlertRoutesPrefix = `${MajorHazardRoutesPrefix}/alert`;
const MajorHazardVisulizationRoutesPrefix = `${MajorHazardRoutesPrefix}/visualization`;
export const MajorHazardRoutes = {
  DASHBOARD: `${MajorHazardAlertRoutesPrefix}/${moduleHomeIndex}`,
  MONITOR: `${MajorHazardAlertRoutesPrefix}/monitor`,
  SENSOR: `${MajorHazardAlertRoutesPrefix}/sensor`,
  ALARM: `${MajorHazardAlertRoutesPrefix}/alarm`,
  SENSOR_ALARM: `${MajorHazardAlertRoutesPrefix}/sensor_alarm`,
  PERSONNEL_ALARM: `${MajorHazardAlertRoutesPrefix}/personnel_alarm`,

  DISPLAY_CATEGORY: `${MajorHazardVisulizationRoutesPrefix}/display_category`,
  MONITOR_UNIT: `${MajorHazardVisulizationRoutesPrefix}/monitor_unit`,
};

const EmergencyRoutesPrefix = "/emergency";
export const EmergencyRoutes = {
  ON_DUTY: `${EmergencyRoutesPrefix}/on_duty`,
  EMERGENCY_TEAM: `${EmergencyRoutesPrefix}/emergency_team`,
  EMERGENCY_SUPPLY: `${EmergencyRoutesPrefix}/emergency_supply`,
  EMERGENCY_PLAN: `${EmergencyRoutesPrefix}/emergency_plan`,
};

const PersonnelLocationRoutesPrefix = "/personnel_location";
export const PersonnelLocationRoutes = {
  PERSONNEL_LOCATION: `${PersonnelLocationRoutesPrefix}/personnel_location`,
};

const AIRecognitionRoutesPrefix = "/ai_recognition";
export const AIRecognitionRoutes = {
  VIDEO_AI: `${AIRecognitionRoutesPrefix}/video_ai`,
};

const IntelligentInspectionRoutesPrefix = "/intelligent_inspection";
export const IntelligentInspectionRoutes = {
  INSPECTION_DASHBOARD: `${IntelligentInspectionRoutesPrefix}/${moduleHomeIndex}`,
  INSPECTION_STANDARD: `${IntelligentInspectionRoutesPrefix}/inspection_standard`,
  INSPECTION_PLACE: `${IntelligentInspectionRoutesPrefix}/inspection_place`,
  INSPECTION_PATH: `${IntelligentInspectionRoutesPrefix}/inspection_path`,
  INSPECTION_PLAN: `${IntelligentInspectionRoutesPrefix}/inspection_plan`,
  INSPECTION_TASK: `${IntelligentInspectionRoutesPrefix}/inspection_task`,
  INSPECTION_TASK_RECORD: `${IntelligentInspectionRoutesPrefix}/inspection_task_record`,
  UNSCHEDULED_TASK_RECORD: `${IntelligentInspectionRoutesPrefix}/unscheduled_task_record`,
};

const TrainingRoutesPrefix = "/training";
const TrainingMaterialRoutesPrefix = `${TrainingRoutesPrefix}/material`;
const TrainingPlanRoutesPrefix = `${TrainingRoutesPrefix}/plan`;
const TrainingStudentRoutesPrefix = `${TrainingRoutesPrefix}/student`;
const TrainingMyRoutesPrefix = `${TrainingRoutesPrefix}/my`;
export const TrainingRoutes = {
  TRAINING_DASHBOARD: `${TrainingRoutesPrefix}/${moduleHomeIndex}`,
  TRAINING_CONFIG: `${TrainingRoutesPrefix}/training_config`,
  TRAINING_SUBJECT: `${TrainingRoutesPrefix}/training_subject`,
  TRAINING_TEACHER: `${TrainingRoutesPrefix}/training_teacher`,
  TRAINING_COURSEWARE: `${TrainingRoutesPrefix}/training_courseware`,
  TRAINING_QUESTION: `${TrainingRoutesPrefix}/training_question`,
  TRAINING_PAPER: `${TrainingRoutesPrefix}/training_paper`,
  TRAINING_COURSE: `${TrainingRoutesPrefix}/training_course`,

  TRAINING_CERTIFICATE: `${TrainingMaterialRoutesPrefix}/training_certificate`,
  TRAINING_PLAN: `${TrainingPlanRoutesPrefix}/training_plan`,
  TRAINING_RECORD: `${TrainingPlanRoutesPrefix}/training_record`,

  PEOPLE_STUDY_RECORD: `${TrainingStudentRoutesPrefix}/people_study_record`,
  PEOPLE_EXAM_RECORD: `${TrainingStudentRoutesPrefix}/people_exam_record`,
  PEOPLE: `${TrainingStudentRoutesPrefix}/people`,

  MY_TRAINING: `${TrainingMyRoutesPrefix}`,
};

const EquipmentRoutesPrefix = "/equipment";
const EquipmentArchiveRoutesPrefix = `${EquipmentRoutesPrefix}/archive`;
const EquipmentManagementRoutesPrefix = `${EquipmentRoutesPrefix}/management`;
export const EquipmentManagementRoutes = {
  EQUIPMENT_DASHBOARD: `${EquipmentRoutesPrefix}/${moduleHomeIndex}`,

  EQUIPMENT_CATEGORY: `${EquipmentArchiveRoutesPrefix}/equipment_category`,
  EQUIPMENT: `${EquipmentArchiveRoutesPrefix}/equipment`,

  EQUIPMENT_SENSOR: `${EquipmentManagementRoutesPrefix}/equipment_sensor`,
  EQUIPMENT_MAINTAIN: `${EquipmentManagementRoutesPrefix}/equipment_maintain`,
  EQUIPMENT_DETECTION: `${EquipmentManagementRoutesPrefix}/equipment_detection`,
  EQUIPMENT_REPAIR: `${EquipmentManagementRoutesPrefix}/equipment_repair`,
  EQUIPMENT_RESUME: `${EquipmentManagementRoutesPrefix}/equipment_resume`,
  EQUIPMENT_STOP: `${EquipmentManagementRoutesPrefix}/equipment_stop`,
  EQUIPMENT_SCRAP: `${EquipmentManagementRoutesPrefix}/equipment_scrap`,
};

const FireFighterRoutesPrefix = "/fire_fighter";
export const FireFighterRoutes = {
  CUSTOMIZE_XUNQIAO_TOPOLOGY: `${FireFighterRoutesPrefix}/0`,
  CUSTOMIZE_XUNQIAO_CHART: `${FireFighterRoutesPrefix}/:id`,
};

const EnergyManagementRoutesPrefix = "/energy";
export const EnergyManagementRoutes = {
  ROOT: `${EnergyManagementRoutesPrefix}`,
  DUMMY: `${EnergyManagementRoutesPrefix}/dummy`,
  // CUSTOMIZE_XUNQIAO_CHART: `${EnergyManagementRoutesPrefix}/:id/:displayType/:name`,
  CUSTOMIZE_XUNQIAO_CHART: `${EnergyManagementRoutesPrefix}/:id/:displayType`,
};

const EnvironmentManagementRoutesPrefix = "/environment";
export const EnvironmentManagementRoutes = {
  // ENVIRONMENT_DASHBOARD: `${EnvironmentRoutesPrefix}/${moduleHomeIndex}`,
  // ENVIRONMENT: `${EnvironmentRoutesPrefix}/environment`,
  DUMMY: `${EnvironmentManagementRoutesPrefix}/dummy`,
  CUSTOMIZE_XUNQIAO_CHART: `${EnvironmentManagementRoutesPrefix}/:id`,
};

const ContractorRoutesPrefix = "/contractor";
const ContractorBasicRoutesPrefix = `${ContractorRoutesPrefix}/basic`; // 基本信息
const ContractorEntryRoutesPrefix = `${ContractorRoutesPrefix}/entry`; // 入场管理
const ContractorProjectRoutesPrefix = `${ContractorRoutesPrefix}/project`; // 项目管理
const ContractorEvaluationRoutesPrefix = `${ContractorRoutesPrefix}/evaluation`; // 评价管理
export const ContractorRoutes = {
  CONTRACTOR_DASHBOARD: `${ContractorRoutesPrefix}/${moduleHomeIndex}`,

  CONTRACTOR_CONFIG: `${ContractorRoutesPrefix}/config`,
  CONTRACTOR: `${ContractorBasicRoutesPrefix}/contractor`,
  CONTRACTOR_CERTIFICATE: `${ContractorBasicRoutesPrefix}/contractor_certificate`,
  CONTRACTOR_EMPLOYEE: `${ContractorBasicRoutesPrefix}/contractor_employee`,
  CONTRACTOR_EMPLOYEE_CERTIFICATE: `${ContractorBasicRoutesPrefix}/contractor_employee_certificate`,

  CONTRACTOR_ENTRY_TRAINING: `${ContractorEntryRoutesPrefix}/training`,
  CONTRACTOR_ENTRY_APPLY: `${ContractorEntryRoutesPrefix}/apply`,

  JOB_TMPL_LIST: `${ContractorProjectRoutesPrefix}/job_tmpl_list`,
  CONTRACTOR_PROJECT: `${ContractorProjectRoutesPrefix}/project`,
  CONTRACTOR_ACCIDENT: `${ContractorProjectRoutesPrefix}/accident`,
  CONTRACTOR_VIOLATION: `${ContractorProjectRoutesPrefix}/violation`,
  CONTRACTOR_PROJECT_STOP: `${ContractorProjectRoutesPrefix}/project_stop`,
  CONTRACTOR_PROJECT_RESUMPTION: `${ContractorProjectRoutesPrefix}/project_resumption`,

  CONTRACTOR_EVALUATION: `${ContractorEvaluationRoutesPrefix}/evaluation`,
  CONTRACTOR_BLACKLIST_ADD: `${ContractorEvaluationRoutesPrefix}/blacklist_add`,
  CONTRACTOR_BLACKLIST_APPEAL: `${ContractorEvaluationRoutesPrefix}/blacklist_appeal`,
  CONTRACTOR_EMPLOYEE_BLACKLIST_ADD: `${ContractorEvaluationRoutesPrefix}/employee_blacklist_add`,
  CONTRACTOR_EMPLOYEE_BLACKLIST_APPEAL: `${ContractorEvaluationRoutesPrefix}/employee_blacklist_appeal`,
};

const AlarmRoutesPrefix = "/alarm";
const AlarmSettingsRoutesPrefix = `${AlarmRoutesPrefix}/settings`;
const AlarmProcessRoutesPrefix = `${AlarmRoutesPrefix}/process`;
const AlarmAnalysisRoutesPrefix = `${AlarmRoutesPrefix}/analysis`;
export const AlarmManagementRoutes = {
  ALARM_DASHBOARD: `${AlarmRoutesPrefix}/${moduleHomeIndex}`,

  ALARM_SETTINGS_SENSOR: `${AlarmSettingsRoutesPrefix}/sensor`,
  ALARM_SETTINGS_RULES: `${AlarmSettingsRoutesPrefix}/rules`,
  ALARM_SETTINGS_MONITORTYPE: `${AlarmSettingsRoutesPrefix}/monitortype`,
  ALARM_SETTINGS_REASON: `${AlarmSettingsRoutesPrefix}/reason`,
  ALARM_SETTINGS_MEASURE: `${AlarmSettingsRoutesPrefix}/measure`,

  ALARM_PROCESS_SENSOR_ALARM: `${AlarmProcessRoutesPrefix}/sensor_alarm`,

  ALARM_ANALYSIS_ALARM_COUNT: `${AlarmAnalysisRoutesPrefix}/alarm_count`,
  ALARM_ANALYSIS_ALARM_DURATION: `${AlarmAnalysisRoutesPrefix}/alarm_duration`,
  ALARM_ANALYSIS_ALARM_TYPE: `${AlarmAnalysisRoutesPrefix}/alarm_type`,
  ALARM_ANALYSIS_ALARM_PRIORITY: `${AlarmAnalysisRoutesPrefix}/alarm_priority`,
};

export const getAlarmSettingsReasonPath = (itemId: string | number) => {
  return `${AlarmSettingsRoutesPrefix}/reason/${itemId}`;
};

const BigScreenRoutesPrefix = "/big_screen";
export const BigScreenRoutes = {
  HOME: `${BigScreenRoutesPrefix}`,
};

export const MessageRoute = "/message";
export const LoginRoute = "/login";
export const PasswordFreeRoute = "/auth";
export const AuthDingtalkRoute = "/auth/dingtalk";
export const PrintPreviewRoute = "/print_preview";
export const VideoRoute = "/video";
export const PaperResultRoute = "/paper_result";
export const H5MapRoute = "/h5_map";
export const NotFoundRoute = "/404";
export const NotPermissionRoute = "/403";

export const notNeedLoginRoutes = [
  LoginRoute,
  PasswordFreeRoute,
  H5MapRoute,
  AuthDingtalkRoute,
];

export const notNeedPermissionRoutes = [
  "/",
  LoginRoute,
  BigScreenRoutes.HOME,
  VideoRoute,
  H5MapRoute,
  MessageRoute,
  AuthDingtalkRoute,
];

// 定义重定向白名单 (例如，允许的路径前缀)
export const redirectWhitelistRoutes = [
  PrintPreviewRoute, //  "/print_preview",
  PaperResultRoute, //  "/paper_result",
  // "/dashboard",
  // 根据您的需求添加更多允许的路径
];
