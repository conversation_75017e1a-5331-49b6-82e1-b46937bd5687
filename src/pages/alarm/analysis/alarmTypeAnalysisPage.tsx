import React from "react";
import FilterBar from "../components/FilterBar";
import MonitorTypePie from "../components/MonitorTypePie";

const AlarmTypeAnalysisPage = () => {
  const [filter, setFilter] = React.useState<{
    areaId: number | null | undefined;
    beginDate: string | null;
    endDate: string | null;
  }>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });
  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} />
      <MonitorTypePie
        filter={{
          areaId: filter.areaId!,
          beginDate: filter.beginDate!,
          endDate: filter.endDate!,
        }}
      />
    </div>
  );
};

export default AlarmTypeAnalysisPage;
