import React from "react";
import AlarmDurationStatsTable from "../components/AlarmDurationStatsTable";
import FilterBar from "../components/FilterBar";

const AlarmDurationAnalysisPage = () => {
  const [filter, setFilter] = React.useState({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });
  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} showTopN />
      <AlarmDurationStatsTable filter={filter} />
    </div>
  );
};

export default AlarmDurationAnalysisPage;
