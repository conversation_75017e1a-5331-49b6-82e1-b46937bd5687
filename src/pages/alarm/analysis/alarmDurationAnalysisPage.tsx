import React from "react";
import AlarmDurationStatsTable from "../components/AlarmDurationStatsTable";
import FilterBar from "../components/FilterBar";

const AlarmDurationAnalysisPage = () => {
  const [filter, setFilter] = React.useState<{
    areaId: number | null | undefined;
    beginDate: string | null;
    endDate: string | null;
    topN?: number;
  }>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });
  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} showTopN />
      <AlarmDurationStatsTable
        filter={{
          areaId: filter.areaId,
          beginDate: filter.beginDate,
          endDate: filter.endDate,
          topN: filter.topN,
        }}
      />
    </div>
  );
};

export default AlarmDurationAnalysisPage;
