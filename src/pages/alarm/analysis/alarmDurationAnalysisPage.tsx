import React from "react";
import AlarmDurationStatsTable from "../components/AlarmDurationStatsTable";
import FilterBar, { FilterValue } from "../components/FilterBar";

const AlarmDurationAnalysisPage = () => {
  const [filter, setFilter] = React.useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} showTopN />
      <AlarmDurationStatsTable
        filter={{
          areaId: filter.areaId!,
          beginDate: filter.beginDate!,
          endDate: filter.endDate!,
          topN: filter.topN,
        }}
      />
    </div>
  );
};

export default AlarmDurationAnalysisPage;
