import React from "react";
import AlarmNumStatsTable from "../components/AlarmNumStatsTable";
import FilterBar from "../components/FilterBar";

const AlarmCountAnalysisPage = () => {
  const [filter, setFilter] = React.useState<{
    areaId: number | null | undefined;
    beginDate: string | null;
    endDate: string | null;
    topN?: number;
  }>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });

  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} showTopN />

      <AlarmNumStatsTable
        filter={{
          areaId: filter.areaId!,
          beginDate: filter.beginDate!,
          endDate: filter.endDate!,
          topN: filter.topN,
        }}
      />
    </div>
  );
};

export default AlarmCountAnalysisPage;
