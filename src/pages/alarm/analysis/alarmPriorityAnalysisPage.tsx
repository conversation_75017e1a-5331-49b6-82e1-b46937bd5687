import React from "react";
import AlarmPriorityPie from "../components/AlarmPriorityPie";
import FilterBar from "../components/FilterBar";

const AlarmPriorityAnalysisPage = () => {
  const [filter, setFilter] = React.useState<{
    areaId: number | null | undefined;
    beginDate: string | null;
    endDate: string | null;
  }>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });
  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} />
      <AlarmPriorityPie
        filter={{
          areaId: filter.areaId!,
          beginDate: filter.beginDate!,
          endDate: filter.endDate!,
        }}
      />
    </div>
  );
};

export default AlarmPriorityAnalysisPage;
