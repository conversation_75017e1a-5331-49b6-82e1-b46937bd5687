import { TableChart } from "components/chart/TableChart";
import {
  getAreaDurationStat,
  getDepartmentDurationStat,
  getDeviceDurationStat,
} from "../../../api/alarm/alarmStat";
import { ValidFilter } from "./FilterBar";

interface DeviceStatsTableProps {
  filter: ValidFilter;
}

// 设备统计表格组件，展示各设备报警数量，字段严格对标接口 equipment.name、num
export default function AlarmDurationStatsTable({
  filter,
}: DeviceStatsTableProps) {
  const tabList = [
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaDurationStat"],
      queryFn: getAreaDurationStat,
      columns: [
        {
          title: "区域名称",
          dataIndex: "area.name",
          align: "left" as const,
        },
        {
          title: "报警时长",
          dataIndex: "alarmTime",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-blue-600">{value}</span>
          ),
        },
      ],
    },
    {
      label: "设备统计",
      value: "device",
      queryKey: ["getDeviceDurationStat"],
      queryFn: getDeviceDurationStat,
      columns: [
        {
          title: "设备名称",
          dataIndex: "equipment.name",
          align: "left" as const,
        },
        {
          title: "报警时长",
          dataIndex: "alarmTime",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-red-600">{value}</span>
          ),
        },
      ],
    },
    {
      label: "部门统计",
      value: "department",
      queryKey: ["getDepartmentDurationStat"],
      queryFn: getDepartmentDurationStat,
      columns: [
        {
          title: "部门名称",
          dataIndex: "department.name",
          align: "left" as const,
        },
        {
          title: "报警时长",
          dataIndex: "alarmTime",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-red-600">{value}</span>
          ),
        },
      ],
    },
  ];

  return (
    <TableChart
      title="报警时长统计"
      filter={filter}
      tabList={tabList}
      height={400}
      emptyText="暂无报警时长数据"
    />
  );
}
