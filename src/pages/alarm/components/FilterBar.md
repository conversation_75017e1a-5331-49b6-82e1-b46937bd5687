# FilterBar 组件文档

## 概述

FilterBar 是报警首页的筛选组件，支持区域、时间范围和 TopN 筛选功能。

## Props

### FilterBarProps

```typescript
interface FilterBarProps {
  value: {
    areaId: number | null;
    beginDate: string | null;
    endDate: string | null;
    topN?: number;
  };
  onChange: (v: {
    areaId: number | null;
    beginDate: string | null;
    endDate: string | null;
    topN?: number;
  }) => void;
  showTopN?: boolean; // 控制是否显示topN筛选项
}
```

### 参数说明

- `value`: 当前筛选值对象
  - `areaId`: 选中的区域ID，可为null
  - `beginDate`: 开始日期，RFC3339格式字符串
  - `endDate`: 结束日期，RFC3339格式字符串
  - `topN`: 显示前N条记录，默认为10，最小值为1
- `onChange`: 筛选值变化时的回调函数
- `showTopN`: 是否显示TopN筛选项，默认为false

## 使用示例

### 基础用法（不显示TopN筛选）

```typescript
import FilterBar from './components/FilterBar';

const AlarmPage = () => {
  const [filterValue, setFilterValue] = useState({
    areaId: null,
    beginDate: null,
    endDate: null,
  });

  return (
    <div>
      <FilterBar
        value={filterValue}
        onChange={setFilterValue}
      />
      {/* 其他内容 */}
    </div>
  );
};
```

### 带TopN筛选的用法

```typescript
import FilterBar from './components/FilterBar';

const AlarmPage = () => {
  const [filterValue, setFilterValue] = useState({
    areaId: null,
    beginDate: null,
    endDate: null,
    topN: 10, // 默认显示前10条
  });

  return (
    <div>
      <FilterBar
        value={filterValue}
        onChange={setFilterValue}
        showTopN={true} // 显示TopN筛选项
      />
      {/* 其他内容 */}
    </div>
  );
};
```

### 完整示例

```typescript
import React, { useState, useEffect } from 'react';
import FilterBar from './components/FilterBar';

const AlarmListPage = () => {
  const [filterValue, setFilterValue] = useState({
    areaId: null,
    beginDate: null,
    endDate: null,
    topN: 20, // 显示前20条
  });

  const [alarmList, setAlarmList] = useState([]);

  // 监听筛选条件变化，重新获取数据
  useEffect(() => {
    fetchAlarmData(filterValue);
  }, [filterValue]);

  const fetchAlarmData = async (filters) => {
    try {
      const params = {
        areaId: filters.areaId,
        beginDate: filters.beginDate,
        endDate: filters.endDate,
        limit: filters.topN || 10, // 使用topN作为limit参数
      };
      
      const response = await getAlarmList(params);
      setAlarmList(response.data);
    } catch (error) {
      console.error('获取报警数据失败:', error);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">报警管理</h1>
      
      <FilterBar
        value={filterValue}
        onChange={setFilterValue}
        showTopN={true} // 启用TopN筛选
      />
      
      {/* 报警列表 */}
      <div className="mt-6">
        {alarmList.map((alarm) => (
          <div key={alarm.id} className="border p-4 mb-2 rounded">
            {/* 报警项内容 */}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AlarmListPage;
```

## 特性

### TopN 筛选特性

1. **默认值**: TopN 默认为 10
2. **最小值限制**: 输入值必须大于 0，自动校正为最小值 1
3. **最大值限制**: 最大值为 1000
4. **条件显示**: 只有当 `showTopN` 为 true 时才显示
5. **自动校正**: 无效输入会自动校正为默认值 10

### 数据验证

- TopN 值会通过 `Math.max(1, Number(v) || 10)` 进行校验
- 确保值始终为正整数
- 无效输入时回退到默认值

### 样式特性

- 使用 Tailwind CSS 进行样式设计
- 响应式布局，适配不同屏幕尺寸
- 与现有筛选项保持一致的视觉风格

## 注意事项

1. TopN 筛选项默认不显示，需要通过 `showTopN` 参数控制
2. TopN 值会自动校验，确保始终为有效的正整数
3. 组件使用 Semi UI 的 InputNumber 组件，支持键盘输入和鼠标操作
4. 筛选值变化时会触发 onChange 回调，建议在父组件中处理数据获取逻辑
