import { DatePicker, InputNumber, Select } from "@douyinfe/semi-ui";
import { useAreaListOptions } from "hooks/useDoubleGuardList";
import { isNil } from "lodash";
import { useMemo } from "react";
import { formatRFC3339 } from "utils";
// 筛选区props类型
export interface FilterBarProps {
  value: {
    areaId: number | null | undefined;
    beginDate: string | null;
    endDate: string | null;
    topN?: number;
  };
  onChange: (v: {
    areaId: number | null | undefined;
    beginDate: string | null;
    endDate: string | null;
    topN?: number;
  }) => void;
  showTopN?: boolean; // 控制是否显示topN筛选项
}

/**
 * 报警首页筛选区，支持区域、时间范围和TopN筛选
 */
export default function FilterBar({
  value,
  onChange,
  showTopN = false,
}: FilterBarProps) {
  console.debug("value", value);
  const areaOptions = useAreaListOptions().map((item: any) => ({
    label: item.name,
    value: item.id,
  }));

  // 日期选择值
  const dateRange = useMemo(() => {
    if (value.beginDate && value.endDate) {
      return [value.beginDate, value.endDate];
    }
    return [];
  }, [value.beginDate, value.endDate]);

  return (
    <div className="flex items-center gap-4 mb-6">
      {/* 区域选择 */}
      <Select
        value={isNil(value.areaId) ? undefined : Number(value.areaId)}
        onChange={(v) => onChange({ ...value, areaId: Number(v) })}
        style={{ width: 180 }}
        optionList={areaOptions}
        placeholder="选择区域"
        size="large"
      />
      {/* 时间范围选择 */}
      <DatePicker
        type="dateRange"
        value={dateRange as [string, string]}
        onChange={(v) => {
          let begin = "",
            end = "";
          if (Array.isArray(v)) {
            begin = v[0]
              ? typeof v[0] === "string"
                ? v[0]
                : formatRFC3339(v[0])
              : "";
            end = v[1]
              ? typeof v[1] === "string"
                ? v[1]
                : formatRFC3339(v[1])
              : "";
          }
          onChange({ ...value, beginDate: begin, endDate: end });
        }}
        size="large"
        style={{ width: 320 }}
        placeholder={["开始日期", "结束日期"]}
      />
      {/* TopN筛选 */}
      {showTopN && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">显示前</span>
          <InputNumber
            value={value.topN || 10}
            onChange={(v: number | string | undefined) =>
              onChange({ ...value, topN: Math.max(1, Number(v) || 10) })
            }
            min={1}
            max={1000}
            size="large"
            style={{ width: 80 }}
            placeholder="10"
          />
          <span className="text-sm text-gray-600">条</span>
        </div>
      )}
    </div>
  );
}
