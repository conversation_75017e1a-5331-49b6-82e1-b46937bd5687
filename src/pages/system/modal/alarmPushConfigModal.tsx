import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getEmployeeList, getSensorList } from "api";
import { alarmPushConfigApis } from "api/system";
import {
  alarmPushConfigAtoms,
  employeeFilterAtom,
  employeePickerColumnsAtom,
  sensorFilterAtom,
  sensorShowColumnsAtom,
} from "atoms";
import { ALARM_LEVEL_MAP, PUSHCONFIG_PUSHMETHOD_MAP } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import PickerTable from "components/table/pickerTable";
import SecondPickerTable from "components/table/secondPickerTable";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useCallback, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { filterEditData } from "utils";

export const AlarmPushConfigModal = () => {
  const operation = "Edit";
  const entityCname = "告警推送";
  const newTitle = "新增" + entityCname; //user-defined code here
  const editTitle = "编辑" + entityCname; //user-defined code here
  const requiredRule = { required: true, message: "此为必填项" };
  const gutter = 24;

  const atoms = alarmPushConfigAtoms;
  const apis = alarmPushConfigApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.id ? editTitle : newTitle;
  const rules = [requiredRule];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.id && getFormApiRef.current) {
      const items = omit([], data?.data);
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
          //user-defined code here
        },
        { isOverride: true }
      );
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [editModalAtom?.id, data, getFormApiRef]);

  const mutation = useMutation({
    mutationFn: editModalAtom?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        fnAtom?.refetch?.();
        // getFormApiRef.current?.reset?.();
        getFormApiRef.current?.setValues?.({}, { isOverride: true });
        destroyDraft(uniqueKey);
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  //user-defined code here: extra data

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${uniqueKey}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={atoms.editModal} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {/* add form items here */}
          {({ formState }) => (
            <>
              <Form.Section text="基础信息">
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.Input
                      label="报警推送名称"
                      field="name"
                      trigger="blur"
                      rules={rules}
                    />
                  </Col>
                  {/* <Col span={12}>
                  <Form.Select label="触发条件" field="conditionTypeList" className='w-full' rules={rules} multiple>
                    {
                      PUSHCONFIG_CONDITIONTYPELIST_MAP.map((item) => (
                        <Form.Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Form.Select.Option>
                      ))
                    }
                  </Form.Select>
                </Col> */}
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.InputNumber
                      label="首次推送距触发间隔时间"
                      field="firstPushInterval"
                      trigger="blur"
                      rules={rules}
                      suffix="秒"
                    />
                  </Col>
                  <Col span={12}>
                    <Form.InputNumber
                      label="持续推送间隔时间:"
                      field="continuousPushInterval"
                      trigger="blur"
                      rules={rules}
                      suffix="秒"
                    />
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.InputNumber
                      label="延迟推送间隔时间"
                      field="delayPushInterval"
                      trigger="blur"
                      rules={rules}
                      suffix="秒"
                    />
                  </Col>
                  <Col span={12}>
                    <Form.Select
                      label="分级报警"
                      field="alarmLevel"
                      className="w-full"
                      rules={rules}
                    >
                      {ALARM_LEVEL_MAP.map((item) => (
                        <Form.Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Form.Select.Option>
                      ))}
                    </Form.Select>
                  </Col>
                </Row>
                <Row gutter={gutter}>
                  <Col span={12}>
                    <Form.CheckboxGroup
                      label="推送方式"
                      field="pushMethodList"
                      direction="horizontal"
                      rules={[{ required: true, message: "此为必填项" }]}
                    >
                      {PUSHCONFIG_PUSHMETHOD_MAP.map((item) => (
                        <Form.Checkbox key={item.id} value={item.id}>
                          {item.name}
                        </Form.Checkbox>
                      ))}
                    </Form.CheckboxGroup>
                  </Col>
                </Row>
              </Form.Section>
              <PickerTable
                title="实时监测信息"
                entity="Sensor"
                entityTitle="选择传感器"
                field="sensorIdList"
                entityField="sensors"
                list={formState.values?.sensors}
                listApi={getSensorList}
                columnsAtom={sensorShowColumnsAtom}
                filterAtom={sensorFilterAtom}
              />
              <SecondPickerTable
                title="报警推送人员"
                entity="Employee"
                entityTitle="选择人员"
                field="receivePersonIdList"
                entityField="receivePerson"
                list={formState.values?.receivePerson}
                listApi={getEmployeeList}
                columnsAtom={employeePickerColumnsAtom}
                filterAtom={employeeFilterAtom}
              />
              {editModalAtom?.id ? null : (
                <Draft id={uniqueKey} draftAtom={atoms.editModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
