import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getDrawAreas } from "api";
import { createMonitor, getMonitor, updateMonitor } from "api/basicInfo";
import { areaDrawerModalAtom, mapPickerAtom } from "atoms";
import { monitorEditModal, monitorFnAtom } from "atoms/basicInfo";
import {
  AreaMapPicker,
  AreaSearch,
  IS_ISNOT_MAP,
  MONITOR_TYPE_MAP,
  MapPicker,
  RestSelect,
  VIDEO_TYPE_MAP,
} from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { filterEditData } from "utils";

export const MonitorModal = () => {
  const getFormApiRef = useRef<any>(null);
  const [monitorEdit, setMonitorEdit] = useAtom(monitorEditModal);
  const [monitorFn] = useAtom(monitorFnAtom);
  const [mapPicker, setMapPicker] = useAtom(mapPickerAtom);
  const [areaDrawer, setAreaDrawer] = useAtom(areaDrawerModalAtom);
  // 如果厂家有3D模型就调用模型， 没有就调用百度地图
  const { data: areas } = useQuery({
    queryKey: ["getDrawAreas"],
    queryFn: getDrawAreas,
  });
  const cesiumUriPrefix = useMemo(() => {
    return areas?.data?.cesiumUriPrefix;
  }, [areas]);

  const { isLoading, data } = useQuery({
    queryKey: ["getMonitor", monitorEdit?.id ?? ""],
    queryFn: () => {
      if (monitorEdit?.id) {
        return getMonitor(monitorEdit?.id);
      }
    },
    enabled: !!monitorEdit?.id,
  });
  const mutation = useMutation({
    mutationFn: monitorEdit?.id ? updateMonitor : createMonitor,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        monitorFn?.refetch?.();
        destroyDraft("monitorEdit");
        setMonitorEdit({
          id: "",
          show: false,
        });
      }
    },
  });

  useEffect(() => {
    if (monitorEdit?.id && data?.data?.name && getFormApiRef.current) {
      const items = data?.data;
      getFormApiRef.current.setValues(
        {
          ...filterEditData(items),
        },
        { isOverride: true }
      );
    }
  }, [monitorEdit?.id, data, getFormApiRef]);

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft("monitorEdit");
    setMonitorEdit({
      id: "",
      show: false,
    });
  }, [setMonitorEdit, mutation]);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        if (monitorEdit?.id) {
          mutation.mutate({
            id: monitorEdit?.id,
            values: values,
          });
        } else {
          mutation.mutate(values);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  const title = monitorEdit?.id ? "编辑视频监控" : "新增视频监控";

  return (
    <>
      <DraftTrigger id="monitorEdit" draftAtom={monitorEditModal} />
      <Modal
        title={title}
        visible={monitorEdit?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          getFormApi={handleSetFormApi}
          labelWidth={140}
        >
          {(formState) => (
            <>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.Input
                    field="name"
                    label="摄像头名称"
                    placeholder="请填写摄像头名称"
                    trigger="blur"
                    rules={[{ required: true, message: "此为必填项!" }]}
                  />
                </Col>
                <Col span={12}>
                  <RestSelect
                    field="type"
                    label="摄像头类型"
                    placeholder="请选择摄像头类型"
                    options={MONITOR_TYPE_MAP}
                    isRequired
                  />
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.Input
                    label="视频流地址"
                    field="videoPath"
                    trigger="blur"
                    rules={[{ required: true, message: "此为必填项!" }]}
                  />
                </Col>
                <Col span={12}>
                  <AreaSearch
                    field="areaId"
                    label="摄像头区域"
                    placeholder="请选择摄像头区域"
                    // isRequired
                  />
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={12}>
                  <Form.Select
                    label="是否在一张图中显示"
                    field="isInMap"
                    className="w-full"
                    rules={[{ required: true, message: "此为必填项!" }]}
                  >
                    {IS_ISNOT_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
                <Col span={12}>
                  <Form.Select
                    label="监控区域类型"
                    field="videoType"
                    className="w-full"
                    rules={[{ required: true, message: "此为必填项!" }]}
                  >
                    {VIDEO_TYPE_MAP.map((item) => (
                      <Form.Select.Option key={item.id} value={item.id}>
                        {item.name}
                      </Form.Select.Option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={24}>
                  <div>
                    {cesiumUriPrefix ? (
                      <AreaMapPicker data={areas} field="map" />
                    ) : (
                      <MapPicker field="map" />
                    )}

                    <Form.Input
                      onClick={() => {
                        setMapPicker({ visible: true });
                        setAreaDrawer({
                          ...areaDrawer,
                          show: true,
                        });
                      }}
                      field="map"
                      label="定位"
                      placeholder={`请设置定位`}
                    />
                  </div>
                </Col>
              </Row>
              {monitorEdit?.id ? null : (
                <Draft id="monitorEdit" draftAtom={monitorEditModal} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
